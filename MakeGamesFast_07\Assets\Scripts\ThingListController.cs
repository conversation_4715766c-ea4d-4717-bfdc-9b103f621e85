using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

public class ThingListController : MonoBehaviour
{
    // runtime
    [Required] public GameObject ContentContainer;
    [Required] public ThingDatabase thingDatabase;
    public ThingEntryVisual FirstSelectedEntry => _firstSelectedEntry;
    private ThingEntryVisual _firstSelectedEntry;
    public ThingEntryVisual SecondSelectedEntry => _secondSelectedEntry;
    private ThingEntryVisual _secondSelectedEntry;
    public Action OnSelectionChangedCallback;

    public List<ThingEntryVisual> ThingEntryVisuals = new List<ThingEntryVisual>();

    void Awake()
    {
        _firstSelectedEntry = null;
        _secondSelectedEntry = null;

        thingDatabase.OnDatabaseChanged += () => Populate(thingDatabase.entries);
        Populate(thingDatabase.entries);
    }

    /// <summary>
    /// Populate the list with the given data.
    /// Clears the old items and adds new ones.
    /// </summary>
    public void Populate(List<ThingData> thingDatas)
    {
        Transform[] children = ContentContainer.transform.GetComponentsInChildren<Transform>();

        foreach (Transform child in children)
        {
            if (child != ContentContainer.transform)
            {
                Destroy(child.gameObject);
            }
        }

        foreach (ThingData thingData in thingDatas)
        {
            // clone your UXML <ThingEntry>
            ThingEntryVisual thingEntry = new ThingEntryVisual.Builder(thingData, ContentContainer).Build();

            // hook up selection callback
            //thingEntry.Selected += () => OnEntrySelected(thingEntry);

            // add to scrollable list
            ThingEntryVisuals.Add(thingEntry);

            _firstSelectedEntry = null;
            _secondSelectedEntry = null;
            OnSelectionChangedCallback?.Invoke();
        }
    }

    //Implment Selection handling
}
