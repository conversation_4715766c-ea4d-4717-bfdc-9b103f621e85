using UnityEngine;
using UnityEngine.UI;

[AddComponentMenu("Layout/Variable Grid Layout")]
public class VariableGridLayout : LayoutGroup
{
    public enum StartAxis { Horizontal, Vertical }
    public StartAxis startAxis = StartAxis.Horizontal;

    [Tooltip("Spacing between elements on the X (horizontal) axis.")]
    public float spacingX = 5f;
    [Toolt<PERSON>("Spacing between elements on the Y (vertical) axis.")]
    public float spacingY = 5f;

    protected override void OnEnable()
    {
        base.OnEnable();
        SetDirty();
    }

    public override void CalculateLayoutInputHorizontal()
    {
        // Let parent read our preferred width based on children if CSF is attached
        float preferred = CalculatePreferredWidth();
        SetLayoutInputForAxis(preferred, preferred, -1f, 0);
    }

    public override void CalculateLayoutInputVertical()
    {
        float preferred = CalculatePreferredHeight();
        SetLayoutInputForAxis(preferred, preferred, -1f, 1);
    }

    public override void SetLayoutHorizontal() => LayoutChildren();
    public override void SetLayoutVertical() => LayoutChildren();

    private float CalculatePreferredWidth()
    {
        float width = padding.horizontal;
        float rowWidth = 0f;
        float rowMaxH = 0f;
        float wrapLimit = rectTransform.rect.width - padding.horizontal;
        bool wrap = wrapLimit > 0 && GetComponent<ContentSizeFitter>() == null;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float w = LayoutUtility.GetPreferredSize(child, 0);
            float h = LayoutUtility.GetPreferredSize(child, 1);

            if (wrap && rowWidth + w > wrapLimit && rowWidth > 0)
            {
                width = Mathf.Max(width, rowWidth + padding.horizontal);
                rowWidth = 0;
                rowMaxH = 0;
            }

            rowWidth += (rowWidth > 0 ? spacingX : 0) + w;
            rowMaxH = Mathf.Max(rowMaxH, h);
        }
        width = Mathf.Max(width, rowWidth + padding.horizontal);
        return width;
    }

    private float CalculatePreferredHeight()
    {
        float height = padding.vertical;
        float rowWidth = 0f;
        float rowMaxH = 0f;
        float wrapLimit = rectTransform.rect.width - padding.horizontal;
        bool wrap = wrapLimit > 0 && GetComponent<ContentSizeFitter>() == null;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float w = LayoutUtility.GetPreferredSize(child, 0);
            float h = LayoutUtility.GetPreferredSize(child, 1);

            if (wrap && rowWidth + w > wrapLimit && rowWidth > 0)
            {
                height += rowMaxH + spacingY;
                rowWidth = 0;
                rowMaxH = 0;
            }

            rowWidth += (rowWidth > 0 ? spacingX : 0) + w;
            rowMaxH = Mathf.Max(rowMaxH, h);
        }
        height += rowMaxH;
        return height;
    }

    private void LayoutChildren()
    {
        float width = rectTransform.rect.width;
        float x = padding.left;
        float y = padding.top;
        float lineMaxH = 0f;

        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];
            float w = LayoutUtility.GetPreferredSize(child, 0);
            float h = LayoutUtility.GetPreferredSize(child, 1);

            if (startAxis == StartAxis.Horizontal)
            {
                if (x + w > width - padding.right && x > padding.left)
                {
                    x = padding.left;
                    y += lineMaxH + spacingY;
                    lineMaxH = 0;
                }
            }
            else // Vertical start
            {
                if (y + h > rectTransform.rect.height - padding.bottom && y > padding.top)
                {
                    y = padding.top;
                    x += lineMaxH + spacingX;
                    lineMaxH = 0;
                }
            }

            SetChildAlongAxis(child, 0, x, w);
            SetChildAlongAxis(child, 1, y, h);

            if (startAxis == StartAxis.Horizontal)
            {
                x += w + spacingX;
                lineMaxH = Mathf.Max(lineMaxH, h);
            }
            else
            {
                y += h + spacingY;
                lineMaxH = Mathf.Max(lineMaxH, w);
            }
        }
    }

    protected override void OnTransformChildrenChanged()
    {
        base.OnTransformChildrenChanged();
        SetDirty();
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();
        SetDirty();
    }
}
