using UnityEngine;
using System;
using TMPro;

public class ThingEntryVisual : MonoBehaviour
{
    private ThingData _data;
    public ThingData Data
    {
        get
        {
            return _data;
        }
        set
        {
            if (_data != value)
            {
                _data = value;
                DataChanged?.Invoke();
            }
        }
    }
    public Action DataChanged;
    private TextMeshProUGUI NameText;
    private TextMeshProUGUI CountText;
    private GameObject NewIcon;

    public void UpdateVisuals()
    {
        // Update the visuals based on the data
        NameText.text = Data.text;
        CountText.text = Data.ingredients.Count.ToString();
        NewIcon.SetActive(Data.ingredients.Count == 0);
    }

    public void Select()
    {
        // Add your selection logic here
    }
    public void Deselect()
    {
        // Add your deselection logic here
    }

    public class Builder
    {
        public ThingData data;
        public GameObject container;

        public Builder(ThingData data, GameObject container)
        {
            this.data = data;
            this.container = container;
        }

        public ThingEntryVisual Build()
        {
            if (this.data == null)
            {
                this.data = new ThingData("");
            }
            GameObject thingEntryObject = Instantiate(Game.Instance.thingVisualObjectPrefab, container.transform);
            ThingEntryVisual thingEntry = thingEntryObject.GetComponent<ThingEntryVisual>();
            thingEntry.Data = this.data;

            thingEntry.NameText = thingEntryObject.transform.Find("NameText").GetComponent<TextMeshProUGUI>();
            thingEntry.CountText = thingEntryObject.transform.Find("CountText").GetComponent<TextMeshProUGUI>();
            thingEntry.NewIcon = thingEntryObject.transform.Find("NewIcon").gameObject;

            thingEntry.UpdateVisuals();

            return thingEntry;
        }
    }
}
